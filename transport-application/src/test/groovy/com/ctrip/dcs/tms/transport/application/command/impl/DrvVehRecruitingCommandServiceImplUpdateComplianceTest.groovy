package com.ctrip.dcs.tms.transport.application.command.impl

import com.ctrip.dcs.tms.transport.api.model.DrvVehRecruitingAddSOARequestType
import com.ctrip.dcs.tms.transport.api.model.DrvVehRecruitingUpdateSOARequestType
import com.ctrip.dcs.tms.transport.api.model.OcrPassStatusModelSOA
import com.ctrip.dcs.tms.transport.application.command.CertificateCheckCommandService
import com.ctrip.dcs.tms.transport.application.command.DriverSafetyCommandService
import com.ctrip.dcs.tms.transport.application.command.RecruitingCommandService
import com.ctrip.dcs.tms.transport.application.command.TmsModRecordCommandService
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService
import com.ctrip.dcs.tms.transport.application.dto.ComplianceCheckResultDTO
import com.ctrip.dcs.tms.transport.application.query.AuthorizationCheckService
import com.ctrip.dcs.tms.transport.application.query.DriverPasswordService
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService
import com.ctrip.dcs.tms.transport.application.query.DrvVehRecruitingQueryService
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsCertificateCheckPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehicleRecruitingPO
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvRecruitingRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsCertificateCheckRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsRecruitingApproveStepChildRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsRecruitingApproveStepRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRecruitingRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.VehicleAuditStatusEnum
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll
import org.apache.commons.lang3.BooleanUtils

import java.lang.reflect.Method

/**
 * DrvVehRecruitingCommandServiceImpl的updateCompliance方法测试
 * 测试车辆合规性检查的各种场景
 */
class DrvVehRecruitingCommandServiceImplUpdateComplianceTest extends Specification {

    @Subject
    def testObj = new DrvVehRecruitingCommandServiceImpl()

    // Mock所有依赖对象
    def vehicleRecruitingRepository = Mock(VehicleRecruitingRepository)
    def drvRecruitingRepository = Mock(DrvRecruitingRepository)
    def recordCommandService = Mock(TmsModRecordCommandService)
    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def vehicleRepository = Mock(VehicleRepository)
    def recruitingCommandService = Mock(RecruitingCommandService)
    def checkRepository = Mock(TmsCertificateCheckRepository)
    def driverPasswordService = Mock(DriverPasswordService)
    def productionLineUtil = Mock(ProductionLineUtil)
    def driverSafetyCommandService = Mock(DriverSafetyCommandService)
    def certificateCheckCommandService = Mock(CertificateCheckCommandService)
    def qmqProducerCommandService = Mock(TmsQmqProducerCommandService)
    def qconfig = Mock(TmsTransportQconfig)
    def drvVehRecruitingQueryService = Mock(DrvVehRecruitingQueryService)
    def enumRepository = Mock(EnumRepository)
    def stepRepository = Mock(TmsRecruitingApproveStepRepository)
    def childRepository = Mock(TmsRecruitingApproveStepChildRepository)
    def authorizationCheckService = Mock(AuthorizationCheckService)
    def driverQueryService = Mock(DriverQueryService)
    def mobileUtil = Mock(MobileHelper)
    def overageQConfig = Mock(OverageQConfig)
    def commonConfig = Mock(CommonConfig)
    def internationalEntryService = Mock(InternationalEntryService)

    def setup() {
        // 注入所有mock依赖
        testObj.vehicleRecruitingRepository = vehicleRecruitingRepository
        testObj.drvRecruitingRepository = drvRecruitingRepository
        testObj.recordCommandService = recordCommandService
        testObj.drvDrvierRepository = drvDrvierRepository
        testObj.vehicleRepository = vehicleRepository
        testObj.recruitingCommandService = recruitingCommandService
        testObj.checkRepository = checkRepository
        testObj.driverPasswordService = driverPasswordService
        testObj.productionLineUtil = productionLineUtil
        testObj.driverSafetyCommandService = driverSafetyCommandService
        testObj.certificateCheckCommandService = certificateCheckCommandService
        testObj.qmqProducerCommandService = qmqProducerCommandService
        testObj.qconfig = qconfig
        testObj.drvVehRecruitingQueryService = drvVehRecruitingQueryService
        testObj.enumRepository = enumRepository
        testObj.stepRepository = stepRepository
        testObj.childRepository = childRepository
        testObj.authorizationCheckService = authorizationCheckService
        testObj.driverQueryService = driverQueryService
        testObj.mobileUtil = mobileUtil
        testObj.overageQConfig = overageQConfig
        testObj.commonConfig = commonConfig
        testObj.internationalEntryService = internationalEntryService
    }

    def "测试updateCompliance - 无合规规则场景"() {
        given: "准备测试数据"
        def vehicleId = 1001L
        def requestType = buildAddRequestType()
        
        when: "调用updateCompliance方法"
        invokeUpdateComplianceMethod(vehicleId, requestType)
        
        then: "验证交互 - 无合规规则时不执行合规检查"
        1 * internationalEntryService.isInComplianceRuleGary(requestType.getCityId()) >> null
        0 * internationalEntryService.autoComplianceCheck(_, _, _)
        0 * vehicleRecruitingRepository.update(_)
        0 * checkRepository.insertTmsCertificateCheck(_)
    }

    def "测试updateCompliance - 有合规规则且检查通过"() {
        given: "准备测试数据"
        def vehicleId = 1001L
        def requestType = buildAddRequestType()
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType("TEST_COMPLIANCE")
        
        def complianceResult = new ComplianceCheckResultDTO()
        complianceResult.setAuditStatus(1) // PASS
        complianceResult.setReason("检查通过")
        
        when: "调用updateCompliance方法"
        invokeUpdateComplianceMethod(vehicleId, requestType)
        
        then: "验证交互"
        1 * internationalEntryService.isInComplianceRuleGary(requestType.getCityId()) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(
            complianceRule.getComplianceType(), 
            requestType.getVehicleLicense(), 
            requestType.getNewVehOcrFieldValue()
        ) >> complianceResult
        
        1 * vehicleRecruitingRepository.update({ VehicleRecruitingPO po ->
            po.getVehicleId() == vehicleId && po.getAuditStatus() == 1
        })
        
        1 * checkRepository.insertTmsCertificateCheck({ TmsCertificateCheckPO po ->
            po.getCheckId() == vehicleId &&
            po.getCheckType() == TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode() &&
            po.getCertificateType() == TmsTransportConstant.CertificateTypeEnum.AUTO_VEHICLE_AUDITSTATUS.getCode() &&
            po.getCheckContent() == "检查通过"
        })
        
        and: "验证OcrPassStatusList被正确更新"
        requestType.getOcrPassStatusList().size() == 1
        with(requestType.getOcrPassStatusList().get(0)) {
            ocrItem == ApproveItemEnum.vehicle_compliance.getCode()
            passStatus == BooleanUtils.toInteger(VehicleAuditStatusEnum.isPass(1))
        }
    }

    def "测试updateCompliance - 有合规规则但检查失败"() {
        given: "准备测试数据"
        def vehicleId = 1001L
        def requestType = buildAddRequestType()
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType("TEST_COMPLIANCE")
        
        def complianceResult = new ComplianceCheckResultDTO()
        complianceResult.setAuditStatus(2) // FAIL
        complianceResult.setReason("检查失败")
        
        when: "调用updateCompliance方法"
        invokeUpdateComplianceMethod(vehicleId, requestType)
        
        then: "验证交互"
        1 * internationalEntryService.isInComplianceRuleGary(requestType.getCityId()) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(_, _, _) >> complianceResult
        
        1 * vehicleRecruitingRepository.update({ VehicleRecruitingPO po ->
            po.getAuditStatus() == 2
        })
        
        1 * checkRepository.insertTmsCertificateCheck({ TmsCertificateCheckPO po ->
            po.getCheckContent() == "检查失败"
        })
        
        and: "验证OcrPassStatusList被正确更新"
        requestType.getOcrPassStatusList().get(0).passStatus == BooleanUtils.toInteger(VehicleAuditStatusEnum.isPass(2))
    }

    def "测试updateCompliance - 合规检查返回null结果"() {
        given: "准备测试数据"
        def vehicleId = 1001L
        def requestType = buildAddRequestType()
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType("TEST_COMPLIANCE")
        
        when: "调用updateCompliance方法"
        invokeUpdateComplianceMethod(vehicleId, requestType)
        
        then: "验证交互 - 使用默认值"
        1 * internationalEntryService.isInComplianceRuleGary(requestType.getCityId()) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(_, _, _) >> null
        
        1 * vehicleRecruitingRepository.update({ VehicleRecruitingPO po ->
            po.getAuditStatus() == 1  // 默认状态
        })
        
        1 * checkRepository.insertTmsCertificateCheck({ TmsCertificateCheckPO po ->
            po.getCheckContent() == ""  // 空原因
        })
    }

    def "测试updateCompliance带Update请求 - 成功场景"() {
        given: "准备测试数据"
        def vehicleId = 1001L
        def updateRequestType = buildUpdateRequestType()
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType("UPDATE_COMPLIANCE")
        
        def complianceResult = new ComplianceCheckResultDTO()
        complianceResult.setAuditStatus(1) // PASS
        complianceResult.setReason("更新检查通过")
        
        when: "调用updateCompliance方法"
        invokeUpdateComplianceMethod(vehicleId, updateRequestType)
        
        then: "验证交互"
        1 * internationalEntryService.isInComplianceRuleGary(updateRequestType.getCityId()) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(_, _, _) >> complianceResult
        
        1 * vehicleRecruitingRepository.update({ VehicleRecruitingPO po ->
            po.getVehicleId() == vehicleId && po.getAuditStatus() == 1
        })
        
        1 * checkRepository.insertTmsCertificateCheck({ TmsCertificateCheckPO po ->
            po.getCheckType() == TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode()
        })
        
        and: "验证OcrPassStatusList被正确更新"  
        updateRequestType.getOcrPassStatusList().size() == 1
    }

    def "测试updateCompliance - 异常处理"() {
        given: "准备测试数据"
        def vehicleId = 1001L
        def requestType = buildAddRequestType()
        
        when: "调用updateCompliance方法时发生异常"
        invokeUpdateComplianceMethod(vehicleId, requestType)
        
        then: "验证异常被正确处理，不向外抛出"
        1 * internationalEntryService.isInComplianceRuleGary(requestType.getCityId()) >> {
            throw new RuntimeException("测试异常")
        }
        0 * vehicleRecruitingRepository.update(_)
        0 * checkRepository.insertTmsCertificateCheck(_)
        noExceptionThrown()  // 方法应该捕获异常
    }

    @Unroll("测试updateCompliance - 不同审核状态: #auditStatusCode")
    def "测试updateCompliance - 不同审核状态"() {
        given: "准备测试数据"
        def vehicleId = 1001L
        def requestType = buildAddRequestType()
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType("TEST_COMPLIANCE")
        
        def complianceResult = new ComplianceCheckResultDTO()
        complianceResult.setAuditStatus(auditStatusCode)
        complianceResult.setReason(reason)
        
        when: "调用updateCompliance方法"
        invokeUpdateComplianceMethod(vehicleId, requestType)
        
        then: "验证不同状态码的处理"
        1 * internationalEntryService.isInComplianceRuleGary(requestType.getCityId()) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(_, _, _) >> complianceResult
        
        1 * vehicleRecruitingRepository.update({ VehicleRecruitingPO po ->
            po.getAuditStatus() == auditStatusCode
        })
        
        1 * checkRepository.insertTmsCertificateCheck({ TmsCertificateCheckPO po ->
            po.getCheckContent() == reason
        })
        
        and: "验证OcrPassStatusList状态"
        requestType.getOcrPassStatusList().get(0).passStatus == expectedPassStatus
        
        where:
        auditStatusCode | reason     | expectedPassStatus
        1               | "通过"      | BooleanUtils.toInteger(VehicleAuditStatusEnum.isPass(1))
        2               | "失败"      | BooleanUtils.toInteger(VehicleAuditStatusEnum.isPass(2))
        3               | "待审核"     | BooleanUtils.toInteger(VehicleAuditStatusEnum.isPass(3))
    }

    // 构建添加请求对象的辅助方法
    private DrvVehRecruitingAddSOARequestType buildAddRequestType() {
        def requestType = new DrvVehRecruitingAddSOARequestType()
        requestType.setCityId(1001L)
        requestType.setVehicleLicense("京A12345")
        requestType.setNewVehOcrFieldValue("OCR字段值")
        requestType.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())
        return requestType
    }

    // 构建更新请求对象的辅助方法
    private DrvVehRecruitingUpdateSOARequestType buildUpdateRequestType() {
        def requestType = new DrvVehRecruitingUpdateSOARequestType()
        requestType.setCityId(1001L)
        requestType.setVehicleLicense("京A12345")
        requestType.setNewVehOcrFieldValue("OCR字段值")
        requestType.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())
        return requestType
    }

    // 通过反射调用私有方法的辅助方法
    private void invokeUpdateComplianceMethod(Long vehicleId, Object requestType) {
        Method method
        if (requestType instanceof DrvVehRecruitingAddSOARequestType) {
            method = DrvVehRecruitingCommandServiceImpl.class.getDeclaredMethod(
                "updateCompliance", 
                long.class, 
                DrvVehRecruitingAddSOARequestType.class
            )
        } else if (requestType instanceof DrvVehRecruitingUpdateSOARequestType) {
            method = DrvVehRecruitingCommandServiceImpl.class.getDeclaredMethod(
                "updateCompliance", 
                Long.class, 
                DrvVehRecruitingUpdateSOARequestType.class
            )
        } else {
            throw new IllegalArgumentException("不支持的请求类型")
        }
        
        method.setAccessible(true)
        method.invoke(testObj, vehicleId, requestType)
    }
} 